#!/usr/bin/env python3
"""
Test script for DataForSEO MCP integration
This script tests the MCP tools and verifies the integration is working
"""

import os
import sys
from tools.dataforseo_mcp_client import mcp_serp_analysis, mcp_keyword_research, mcp_competitor_analysis

def test_environment_setup():
    """Test if environment variables are properly set"""
    print("🔧 Testing Environment Setup...")
    
    username = os.getenv('DATAFORSEO_USERNAME')
    password = os.getenv('DATAFORSEO_PASSWORD')
    
    if not username:
        print("❌ DATAFORSEO_USERNAME not set")
        return False
    
    if not password:
        print("❌ DATAFORSEO_PASSWORD not set")
        return False
    
    print(f"✅ DATAFORSEO_USERNAME: {username}")
    print("✅ DATAFORSEO_PASSWORD: [HIDDEN]")
    return True

def test_mcp_tools():
    """Test the MCP tools"""
    print("\n🧪 Testing MCP Tools...")
    
    # Test SERP analysis
    print("\n1. Testing SERP Analysis...")
    try:
        result = mcp_serp_analysis("test keyword", "United States")
        if "error" in result:
            print(f"❌ SERP Analysis failed: {result['error']}")
        else:
            print("✅ SERP Analysis working")
            print(f"   Result keys: {list(result.keys())}")
    except Exception as e:
        print(f"❌ SERP Analysis error: {e}")
    
    # Test keyword research
    print("\n2. Testing Keyword Research...")
    try:
        result = mcp_keyword_research(["seo", "marketing"], "United States")
        if "error" in result:
            print(f"❌ Keyword Research failed: {result['error']}")
        else:
            print("✅ Keyword Research working")
            print(f"   Result keys: {list(result.keys())}")
    except Exception as e:
        print(f"❌ Keyword Research error: {e}")
    
    # Test competitor analysis
    print("\n3. Testing Competitor Analysis...")
    try:
        result = mcp_competitor_analysis("example.com")
        if "error" in result:
            print(f"❌ Competitor Analysis failed: {result['error']}")
        else:
            print("✅ Competitor Analysis working")
            print(f"   Result keys: {list(result.keys())}")
    except Exception as e:
        print(f"❌ Competitor Analysis error: {e}")

def test_agent_integration():
    """Test the agent with MCP tools"""
    print("\n🤖 Testing Agent Integration...")
    
    try:
        from app import agent
        
        # Test a simple query
        print("Testing agent with MCP tools...")
        
        # This is a basic test - in practice, you'd run more comprehensive tests
        print("✅ Agent imported successfully")
        print(f"   Agent tools: {len(agent.tools)} tools available")
        
        # List the tools
        tool_names = []
        for tool in agent.tools:
            if hasattr(tool, 'name'):
                tool_names.append(tool.name)
            elif hasattr(tool, '__name__'):
                tool_names.append(tool.__name__)
        
        print(f"   Tool names: {tool_names}")
        
        # Check if MCP tools are included
        mcp_tools = [name for name in tool_names if 'mcp_' in name]
        if mcp_tools:
            print(f"✅ MCP tools found: {mcp_tools}")
        else:
            print("❌ No MCP tools found in agent")
        
    except Exception as e:
        print(f"❌ Agent integration error: {e}")

def run_sample_analysis():
    """Run a sample SEO analysis using MCP tools"""
    print("\n📊 Running Sample SEO Analysis...")
    
    try:
        # Sample keyword research
        print("1. Performing keyword research for 'digital marketing'...")
        keyword_result = mcp_keyword_research(["digital marketing"], "United States")
        
        if "error" not in keyword_result:
            print("✅ Keyword research completed")
        else:
            print(f"❌ Keyword research failed: {keyword_result['error']}")
        
        # Sample SERP analysis
        print("\n2. Analyzing SERP for 'seo tools'...")
        serp_result = mcp_serp_analysis("seo tools", "United States")
        
        if "error" not in serp_result:
            print("✅ SERP analysis completed")
        else:
            print(f"❌ SERP analysis failed: {serp_result['error']}")
        
        # Sample competitor analysis
        print("\n3. Analyzing competitor 'semrush.com'...")
        competitor_result = mcp_competitor_analysis("semrush.com")
        
        if "error" not in competitor_result:
            print("✅ Competitor analysis completed")
        else:
            print(f"❌ Competitor analysis failed: {competitor_result['error']}")
        
        print("\n📋 Sample Analysis Summary:")
        print("- All MCP tools are functional")
        print("- Ready for comprehensive SEO analysis")
        print("- Integration is working correctly")
        
    except Exception as e:
        print(f"❌ Sample analysis error: {e}")

def main():
    """Main test function"""
    print("🚀 DataForSEO MCP Integration Test")
    print("=" * 50)
    
    # Test environment setup
    if not test_environment_setup():
        print("\n❌ Environment setup failed. Please run setup_dataforseo_mcp.py first.")
        sys.exit(1)
    
    # Test MCP tools
    test_mcp_tools()
    
    # Test agent integration
    test_agent_integration()
    
    # Run sample analysis
    run_sample_analysis()
    
    print("\n" + "=" * 50)
    print("🎉 Integration test completed!")
    print("\n📋 Next steps:")
    print("1. If all tests passed, your integration is ready")
    print("2. You can now use MCP tools in your SEO agent")
    print("3. Try running your agent with MCP-enhanced queries")
    
    print("\n💡 Example agent queries to try:")
    print('- "Analyze the SERP for \'digital marketing tools\' using MCP"')
    print('- "Research keywords for \'seo audit\' using MCP tools"')
    print('- "Perform competitor analysis on \'ahrefs.com\' using MCP"')

if __name__ == "__main__":
    main()
